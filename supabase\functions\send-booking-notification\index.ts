import { serve } from "std/http/server.ts";
import { createClient } from "@supabase/supabase-js";
import nodemailer from "nodemailer";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

const emailConfig = {
  host: "smtp.gmail.com",
  port: 465,
  secure: true,
  auth: {
    user: "<EMAIL>",
    pass: "nlnk wltw rgsp zhpp"
  }
};

const ADMIN_EMAIL = "<EMAIL>";

// Helper function to get zone display string
function getZoneDisplay(booking: any): string {
  if (booking.zones?.name) {
    return booking.zones.name;
  } else if (booking.booking_zones && booking.booking_zones.length > 0) {
    return booking.booking_zones.map((bz: any) => bz.zone?.name || 'Unknown Zone').join(', ');
  }
  return 'Unknown Zone';
}

// Customer email template for booking approval
function getBookingApprovedCustomerTemplate(booking: any) {
  const zoneDisplay = getZoneDisplay(booking);

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4c12ff;">Booking Approved! 🎉</h2>
      <p>Dear ${booking.business_name},</p>
      <p>Great news! Your booking has been approved by our team.</p>

      <div style="background: #f3f1ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4c12ff;">Booking Details:</h3>
        <ul style="list-style: none; padding: 0;">
          <li><strong>Booking ID:</strong> ${booking.id}</li>
          <li><strong>Business Name:</strong> ${booking.business_name}</li>
          <li><strong>Zone(s):</strong> ${zoneDisplay}</li>
          <li><strong>Duration:</strong> ${new Date(booking.start_date).toLocaleDateString()} to ${new Date(booking.end_date).toLocaleDateString()}</li>
          <li><strong>Status:</strong> <span style="color: #22c55e; font-weight: bold;">Approved</span></li>
        </ul>
      </div>

      <p>Your advertisement will be displayed during the approved period. We'll notify you once your campaign is live.</p>
      <p>Thank you for choosing 3Shul!</p>

      <p>Best regards,<br>3Shul Team</p>
    </div>
  `;
}

// Customer email template for booking fulfillment
function getBookingFulfilledCustomerTemplate(booking: any) {
  const zoneDisplay = getZoneDisplay(booking);

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4c12ff;">Campaign Live! 🚀</h2>
      <p>Dear ${booking.business_name},</p>
      <p>Your advertisement campaign is now live and running!</p>

      <div style="background: #f3f1ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4c12ff;">Campaign Details:</h3>
        <ul style="list-style: none; padding: 0;">
          <li><strong>Booking ID:</strong> ${booking.id}</li>
          <li><strong>Business Name:</strong> ${booking.business_name}</li>
          <li><strong>Zone(s):</strong> ${zoneDisplay}</li>
          <li><strong>Campaign Period:</strong> ${new Date(booking.start_date).toLocaleDateString()} to ${new Date(booking.end_date).toLocaleDateString()}</li>
          <li><strong>Status:</strong> <span style="color: #3b82f6; font-weight: bold;">Live & Running</span></li>
        </ul>
      </div>

      <p>Your advertisement is now being displayed to thousands of viewers. We hope this campaign brings great results for your business!</p>
      <p>Thank you for choosing 3Shul!</p>

      <p>Best regards,<br>3Shul Team</p>
    </div>
  `;
}

// Customer email template for booking cancellation/rejection
function getBookingCancelledCustomerTemplate(booking: any) {
  const zoneDisplay = getZoneDisplay(booking);

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #ef4444;">Booking Update</h2>
      <p>Dear ${booking.business_name},</p>
      <p>We regret to inform you that your booking has been cancelled.</p>

      <div style="background: #fef2f2; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ef4444;">
        <h3 style="margin-top: 0; color: #ef4444;">Booking Details:</h3>
        <ul style="list-style: none; padding: 0;">
          <li><strong>Booking ID:</strong> ${booking.id}</li>
          <li><strong>Business Name:</strong> ${booking.business_name}</li>
          <li><strong>Zone(s):</strong> ${zoneDisplay}</li>
          <li><strong>Duration:</strong> ${new Date(booking.start_date).toLocaleDateString()} to ${new Date(booking.end_date).toLocaleDateString()}</li>
          <li><strong>Status:</strong> <span style="color: #ef4444; font-weight: bold;">Cancelled</span></li>
        </ul>
      </div>

      <p>If you have any questions about this cancellation, please contact our support team. Any payments made will be refunded according to our refund policy.</p>
      <p>We apologize for any inconvenience caused.</p>

      <p>Best regards,<br>3Shul Team</p>
    </div>
  `;
}

// Admin notification templates
function getAdminNotificationTemplate(booking: any, action: string) {
  const zoneDisplay = getZoneDisplay(booking);
  const actionColors = {
    'approved': '#22c55e',
    'fulfilled': '#3b82f6',
    'cancelled': '#ef4444'
  };

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4c12ff;">Booking ${action.charAt(0).toUpperCase() + action.slice(1)} - Admin Notification</h2>
      <p>A booking has been ${action}:</p>

      <div style="background: #f3f1ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4c12ff;">Booking Details:</h3>
        <ul style="list-style: none; padding: 0;">
          <li><strong>Booking ID:</strong> ${booking.id}</li>
          <li><strong>Business Name:</strong> ${booking.business_name}</li>
          <li><strong>Customer Email:</strong> ${booking.email}</li>
          <li><strong>Customer Phone:</strong> ${booking.phone_number}</li>
          <li><strong>Zone(s):</strong> ${zoneDisplay}</li>
          <li><strong>Duration:</strong> ${new Date(booking.start_date).toLocaleDateString()} to ${new Date(booking.end_date).toLocaleDateString()}</li>
          <li><strong>Status:</strong> <span style="color: ${actionColors[action] || '#6b7280'}; font-weight: bold;">${booking.status}</span></li>
        </ul>
      </div>

      <p>Customer notification email has been sent automatically.</p>

      <p>Best regards,<br>3Shul System</p>
    </div>
  `;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { bookingId, notificationType } = await req.json();

    if (!bookingId || !notificationType) {
      throw new Error('Booking ID and notification type are required');
    }

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Fetch booking details with all relationships
    const { data: booking, error: bookingError } = await supabaseClient
      .from('bookings')
      .select(`
        *,
        zones (
          id,
          name,
          city,
          sub_zone,
          price_year
        ),
        booking_zones (
          zone:zones (
            id,
            name,
            city,
            sub_zone
          )
        ),
        transactions (amount)
      `)
      .eq('id', bookingId)
      .single();

    if (bookingError || !booking) {
      throw new Error('Booking not found');
    }

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: emailConfig.host,
      port: emailConfig.port,
      secure: emailConfig.secure,
      auth: emailConfig.auth,
      tls: {
        rejectUnauthorized: false
      }
    });

    // Verify transporter configuration
    await transporter.verify();
    console.log("Transporter verified successfully");

    let customerTemplate = '';
    let customerSubject = '';
    let adminTemplate = '';
    let adminSubject = '';

    // Determine email templates based on notification type
    switch (notificationType) {
      case 'approved':
        customerTemplate = getBookingApprovedCustomerTemplate(booking);
        customerSubject = '3Shul Booking Approved - Your Campaign is Ready!';
        adminTemplate = getAdminNotificationTemplate(booking, 'approved');
        adminSubject = 'Booking Approved - Admin Notification';
        break;
      case 'fulfilled':
        customerTemplate = getBookingFulfilledCustomerTemplate(booking);
        customerSubject = '3Shul Campaign Live - Your Advertisement is Running!';
        adminTemplate = getAdminNotificationTemplate(booking, 'fulfilled');
        adminSubject = 'Booking Fulfilled - Admin Notification';
        break;
      case 'cancelled':
        customerTemplate = getBookingCancelledCustomerTemplate(booking);
        customerSubject = '3Shul Booking Cancelled - Important Update';
        adminTemplate = getAdminNotificationTemplate(booking, 'cancelled');
        adminSubject = 'Booking Cancelled - Admin Notification';
        break;
      default:
        throw new Error('Invalid notification type');
    }

    const results = [];

    // Send email to customer
    if (booking.email) {
      const customerInfo = await transporter.sendMail({
        from: `"3Shul Team" <${emailConfig.auth.user}>`,
        to: booking.email,
        subject: customerSubject,
        html: customerTemplate,
        headers: {
          'X-Booking-ID': bookingId,
          'X-Notification-Type': notificationType
        }
      });
      console.log("Customer email sent successfully:", customerInfo.messageId);
      results.push({ type: 'customer', messageId: customerInfo.messageId });
    }

    // Send email to admin
    const adminInfo = await transporter.sendMail({
      from: `"3Shul System" <${emailConfig.auth.user}>`,
      to: ADMIN_EMAIL,
      subject: adminSubject,
      html: adminTemplate,
      headers: {
        'X-Booking-ID': bookingId,
        'X-Notification-Type': notificationType
      }
    });
    console.log("Admin email sent successfully:", adminInfo.messageId);
    results.push({ type: 'admin', messageId: adminInfo.messageId });

    return new Response(
      JSON.stringify({
        success: true,
        results: results
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error("Error:", error.message);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});
