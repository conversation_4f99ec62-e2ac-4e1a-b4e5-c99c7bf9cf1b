// Test script for email functions
// This script tests the new send-booking-notification Edge Function

const SUPABASE_URL = 'https://xirnspqtaoyzsqlbbhcl.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inhpcm5zcHF0YW95enNxbGJiaGNsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ2MDI0NzQsImV4cCI6MjA1MDE3ODQ3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

async function testEmailFunction(notificationType, bookingId) {
  console.log(`\n🧪 Testing ${notificationType} email notification...`);
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/send-booking-notification`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
      },
      body: JSON.stringify({
        bookingId: bookingId,
        notificationType: notificationType
      })
    });

    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log(`✅ ${notificationType} email test successful!`);
      console.log('📧 Email results:', result.results);
      return true;
    } else {
      console.log(`❌ ${notificationType} email test failed:`, result.error);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${notificationType} email test error:`, error.message);
    return false;
  }
}

async function testPaymentEmailFunction(bookingId) {
  console.log(`\n🧪 Testing payment confirmation email...`);
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/send-payment-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
      },
      body: JSON.stringify({
        bookingId: bookingId
      })
    });

    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log(`✅ Payment email test successful!`);
      console.log('📧 Customer email ID:', result.customerMessageId);
      console.log('📧 Admin email ID:', result.adminMessageId);
      return true;
    } else {
      console.log(`❌ Payment email test failed:`, result.error);
      return false;
    }
  } catch (error) {
    console.log(`❌ Payment email test error:`, error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Email Function Tests');
  console.log('=====================================');
  
  // You'll need to replace this with an actual booking ID from your database
  const testBookingId = 'test-booking-id-here';
  
  console.log(`📋 Using booking ID: ${testBookingId}`);
  console.log('⚠️  Note: Replace testBookingId with a real booking ID from your database');
  
  const results = [];
  
  // Test payment confirmation email
  results.push(await testPaymentEmailFunction(testBookingId));
  
  // Test approval email
  results.push(await testEmailFunction('approved', testBookingId));
  
  // Test fulfillment email
  results.push(await testEmailFunction('fulfilled', testBookingId));
  
  // Test cancellation email
  results.push(await testEmailFunction('cancelled', testBookingId));
  
  console.log('\n📊 Test Results Summary');
  console.log('========================');
  const passedTests = results.filter(r => r).length;
  const totalTests = results.length;
  
  console.log(`✅ Passed: ${passedTests}/${totalTests}`);
  console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All email functions are working correctly!');
  } else {
    console.log('\n⚠️  Some email functions need attention. Check the logs above.');
  }
}

// Run the tests
runAllTests().catch(console.error);
